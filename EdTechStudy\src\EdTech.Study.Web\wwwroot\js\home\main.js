﻿// Add smooth scrolling for navigation links
document.querySelectorAll('.nav-item').forEach((link) => {
    link.addEventListener('click', (e) => {
        e.preventDefault();

        // Remove active class from all links
        document.querySelectorAll('.nav-item').forEach((item) => {
            item.classList.remove('active');
        });

        // Add active class to clicked link
        link.classList.add('active');
    });
});

// Handle CTA button click
document.querySelector('.cta-button').addEventListener('click', () => {
    // Add your call-to-action logic here
    console.log('CTA button clicked');
});

// Handle subject card clicks
document.querySelectorAll('.subject-card').forEach((card) => {
    card.addEventListener('click', () => {
        // Add your subject navigation logic here
        console.log('Subject clicked:', card.querySelector('h4').textContent);
    });
});

// Handle course button clicks
document.querySelectorAll('.course-button').forEach((button) => {
    button.addEventListener('click', () => {
        // Add your course navigation logic here
        const courseTitle = button
            .closest('.course-card')
            .querySelector('h3').textContent;
        console.log('Course selected:', courseTitle);
    });
});

// Handle article card clicks
document.querySelectorAll('.article-card').forEach((card) => {
    card.addEventListener('click', () => {
        // Add your article navigation logic here
        console.log('Article clicked:', card.querySelector('h3').textContent);
    });
});

// Handle social widget clicks
document.querySelectorAll('.social-widget img').forEach((icon, index) => {
    icon.addEventListener('click', () => {
        // Add your social media link logic here
        console.log('Social icon clicked:', index + 1);
    });
});

// Custom Testimonial Carousel
class TestimonialCarousel {
    constructor() {
        this.carousel = document.getElementById('testimonialCarousel');
        this.prevBtn = document.getElementById('prevBtn');
        this.nextBtn = document.getElementById('nextBtn');
        this.items = document.querySelectorAll('.testimonial-item');
        this.currentIndex = 0;
        this.itemWidth = 33.333; // 100% / 3 items visible
        this.totalItems = this.items.length;
        this.maxIndex = this.totalItems - 3; // Maximum index to show 3 items

        this.init();
    }

    init() {
        // Set initial position
        this.updateCarousel();
        this.updateButtons();

        // Add event listeners
        this.prevBtn.addEventListener('click', () => this.prev());
        this.nextBtn.addEventListener('click', () => this.next());

        // Auto-play functionality (optional)
        this.startAutoPlay();

        // Handle window resize
        window.addEventListener('resize', () => this.handleResize());
    }

    updateCarousel() {
        const translateX = -this.currentIndex * this.itemWidth;
        this.carousel.style.transform = `translateX(${translateX}%)`;
    }

    updateButtons() {
        // Disable/enable buttons based on current position
        this.prevBtn.disabled = this.currentIndex <= 0;
        this.nextBtn.disabled = this.currentIndex >= this.maxIndex;
    }

    prev() {
        if (this.currentIndex > 0) {
            this.currentIndex--;
            this.updateCarousel();
            this.updateButtons();
        }
    }

    next() {
        if (this.currentIndex < this.maxIndex) {
            this.currentIndex++;
            this.updateCarousel();
            this.updateButtons();
        }
    }

    startAutoPlay() {
        setInterval(() => {
            if (this.currentIndex >= this.maxIndex) {
                this.currentIndex = 0;
            } else {
                this.currentIndex++;
            }
            this.updateCarousel();
            this.updateButtons();
        }, 5000); // Auto-advance every 5 seconds
    }

    handleResize() {
        // Handle responsive behavior
        const isMobile = window.innerWidth <= 768;
        if (isMobile) {
            this.itemWidth = 100; // Show 1 item on mobile
            this.maxIndex = this.totalItems - 1;
        } else {
            this.itemWidth = 33.333; // Show 3 items on desktop
            this.maxIndex = this.totalItems - 3;
        }

        // Reset to valid position if needed
        if (this.currentIndex > this.maxIndex) {
            this.currentIndex = this.maxIndex;
        }

        this.updateCarousel();
        this.updateButtons();
    }
}

// Initialize carousel when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    if (document.getElementById('testimonialCarousel')) {
        new TestimonialCarousel();
    }
});
